"use client";

import { useState } from "react";
import { useSession, signOut } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Menu,
  X,
  Home,
  Users,
  Ticket,
  UserPlus,
  BarChart3,
  LogOut,
  Settings,
  Bell,
} from "lucide-react";

export default function DashboardLayout({ children }) {
  const { data: session } = useSession();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();

  const isAdmin = session?.user?.role === "ADMIN";

  const adminNavigation = [
    { name: "Dashboard", href: "/dashboard/admin", icon: Home },
    { name: "Clienti", href: "/dashboard/admin/clients", icon: Users },
    { name: "Dipendenti", href: "/dashboard/admin/employees", icon: UserPlus },
    { name: "Ticket", href: "/dashboard/admin/tickets", icon: Ticket },
    { name: "Report", href: "/dashboard/admin/reports", icon: BarChart3 },
  ];

  const employeeNavigation = [
    { name: "Dashboard", href: "/dashboard/employee", icon: Home },
    { name: "I Miei Ticket", href: "/dashboard/employee/tickets", icon: Ticket },
  ];

  const navigation = isAdmin ? adminNavigation : employeeNavigation;

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? "block" : "hidden"}`}>
        <div className="fixed inset-0 bg-black/50" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 w-64 bg-surface shadow-xl">
          <SidebarContent
            navigation={navigation}
            pathname={pathname}
            session={session}
            onSignOut={handleSignOut}
            onClose={() => setSidebarOpen(false)}
          />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-surface border-r border-gray-200">
          <SidebarContent
            navigation={navigation}
            pathname={pathname}
            session={session}
            onSignOut={handleSignOut}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top header */}
        <header className="bg-surface border-b border-gray-200 px-4 py-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                type="button"
                className="lg:hidden p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-gray-100"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu size={24} />
              </button>
              <h1 className="text-xl font-semibold text-text-primary">
                {isAdmin ? "Pannello Amministratore" : "Pannello Dipendente"}
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <button className="p-2 rounded-md text-text-secondary hover:text-text-primary hover:bg-gray-100">
                <Bell size={20} />
              </button>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-text-primary">
                    {session?.user?.firstName} {session?.user?.lastName}
                  </p>
                  <p className="text-xs text-text-secondary">
                    {isAdmin ? "Amministratore" : "Dipendente"}
                  </p>
                </div>
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-text-onPrimary text-sm font-medium">
                    {session?.user?.firstName?.[0]}{session?.user?.lastName?.[0]}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  );
}

function SidebarContent({ navigation, pathname, session, onSignOut, onClose }) {
  return (
    <>
      {/* Logo */}
      <div className="flex items-center gap-3 px-6 py-6 border-b border-gray-200">
        <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
          <Image
            src="/img/logo/logo.jpg"
            alt="Logo"
            width={32}
            height={32}
            className="rounded-md"
          />
        </div>
        <div>
          <h2 className="text-lg font-bold text-text-primary">IT Solutions</h2>
          <p className="text-xs text-text-secondary">Gestionale</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto p-1 rounded-md text-text-secondary hover:text-text-primary lg:hidden"
          >
            <X size={20} />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={onClose}
              className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isActive
                  ? "bg-primary text-text-onPrimary"
                  : "text-text-secondary hover:text-text-primary hover:bg-gray-100"
              }`}
            >
              <item.icon size={20} />
              {item.name}
            </Link>
          );
        })}
      </nav>

      {/* User info and logout */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
            <span className="text-text-onPrimary text-sm font-medium">
              {session?.user?.firstName?.[0]}{session?.user?.lastName?.[0]}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-text-primary truncate">
              {session?.user?.firstName} {session?.user?.lastName}
            </p>
            <p className="text-xs text-text-secondary truncate">
              {session?.user?.email}
            </p>
          </div>
        </div>

        <div className="space-y-1">
          <button className="flex items-center gap-3 w-full px-3 py-2 text-sm text-text-secondary hover:text-text-primary hover:bg-gray-100 rounded-lg transition-colors">
            <Settings size={16} />
            Impostazioni
          </button>
          <button
            onClick={onSignOut}
            className="flex items-center gap-3 w-full px-3 py-2 text-sm text-error hover:bg-error/10 rounded-lg transition-colors"
          >
            <LogOut size={16} />
            Esci
          </button>
        </div>
      </div>
    </>
  );
} 