# Gestionale IT Solutions

Un sistema di gestione completo per aziende IT che forniscono soluzioni informatiche ai clienti.

## 🚀 Caratteristiche

- **Dashboard Amministratore**: Gestione completa di clienti, dipendenti, ticket e report
- **Dashboard Dipendente**: Visualizzazione e gestione dei ticket assegnati
- **Autenticazione sicura** con NextAuth.js
- **Database PostgreSQL** con Prisma ORM
- **Design responsive** ottimizzato per desktop e mobile
- **UI moderna** con TailwindCSS e Lucide React icons

## 🛠️ Tecnologie Utilizzate

- **Next.js 15** (App Router)
- **React 19**
- **TypeScript/JavaScript**
- **TailwindCSS** per lo styling
- **Prisma** come ORM
- **PostgreSQL** (Neon Database)
- **NextAuth.js** per l'autenticazione
- **Lucide React** per le icone

## 📋 Prerequisiti

- Node.js 18+ 
- npm o yarn
- Database PostgreSQL (consigliato: Neon)

## ⚙️ Installazione

1. **Clona il repository**
   ```bash
   git clone <repository-url>
   cd gestionale
   ```

2. **Installa le dipendenze**
   ```bash
   npm install
   ```

3. **Configura le variabili d'ambiente**
   
   Crea un file `.env` nella root del progetto:
   ```env
   # Database
   DATABASE_URL="******************************************************************************************"
   
   # NextAuth
   NEXTAUTH_SECRET="your_nextauth_secret_here"
   NEXTAUTH_URL="http://localhost:3000"
   
   # App Configuration
   APP_NAME="Gestionale IT Solutions"
   ```

4. **Configura il database**
   ```bash
   # Genera il client Prisma
   npm run db:generate
   
   # Sincronizza lo schema con il database
   npm run db:push
   
   # Inizializza il database con dati di esempio
   npm run db:init
   ```

5. **Avvia l'applicazione**
   ```bash
   npm run dev
   ```

   L'applicazione sarà disponibile su `http://localhost:3000`

## 👥 Credenziali Demo

Dopo aver eseguito `npm run db:init`, puoi accedere con:

### Amministratore
- **Username**: `jojodev`
- **Password**: `pass1234`

### Dipendente
- **Username**: `mario.rossi`
- **Password**: `employee123`

## 📱 Funzionalità

### Dashboard Amministratore
- **Panoramica generale** con statistiche e KPI
- **Gestione clienti**: Creazione, modifica e visualizzazione clienti
- **Gestione dipendenti**: Creazione e gestione account dipendenti
- **Gestione ticket**: Creazione, assegnazione e monitoraggio ticket
- **Report e analytics**: Fatturato, performance dipendenti, statistiche

### Dashboard Dipendente
- **Visualizzazione ticket assegnati**
- **Aggiornamento stato ticket** (Aperto → In Lavorazione → Completato)
- **Statistiche personali** e guadagni

## 🗂️ Struttura del Progetto

```
gestionale/
├── app/
│   ├── api/                    # API Routes
│   │   ├── auth/              # NextAuth configuration
│   │   ├── admin/             # Admin API endpoints
│   │   └── employee/          # Employee API endpoints
│   ├── components/            # Componenti React riutilizzabili
│   ├── dashboard/             # Dashboard pages
│   │   ├── admin/             # Admin dashboard
│   │   └── employee/          # Employee dashboard
│   ├── auth/                  # Authentication pages
│   └── generated/             # Prisma generated client
├── lib/                       # Utility libraries
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── scripts/                   # Database initialization scripts
```

## 🎨 Design System

### Palette Colori
- **Primary**: `#1b365d` (Blu profondo)
- **Primary Light**: `#2f5d91` (Blu medio)
- **Secondary**: `#f26722` (Arancione acceso)
- **Secondary Light**: `#f79056` (Arancione chiaro)
- **Background**: `#f9fafc` (Grigio chiarissimo)
- **Surface**: `#ffffff` (Bianco puro)

### Typography
- **Font principale**: Inter (Google Fonts)
- **Font weight**: 400, 500, 600, 700

## 🔧 Scripts Disponibili

```bash
npm run dev          # Avvia il server di sviluppo
npm run build        # Build per produzione
npm run start        # Avvia il server di produzione
npm run lint         # Esegue ESLint
npm run db:generate  # Genera il client Prisma
npm run db:push      # Sincronizza lo schema con il database
npm run db:init      # Inizializza il database con dati demo
```

## 🚀 Deploy

### Vercel (Consigliato)
1. Connetti il repository a Vercel
2. Configura le variabili d'ambiente
3. Deploy automatico ad ogni push

### Altri provider
1. Esegui `npm run build`
2. Carica i file della cartella `.next` sul server
3. Configura le variabili d'ambiente
4. Avvia con `npm start`

## 🔒 Sicurezza

- Autenticazione JWT con NextAuth.js
- Password hashate con bcryptjs
- Middleware per protezione delle route
- Validazione dei permessi per API
- Sanitizzazione degli input

## 📝 Licenza

Questo progetto è rilasciato sotto licenza MIT.

## 🤝 Contributi

I contributi sono benvenuti! Per favore:
1. Fai un fork del progetto
2. Crea un branch per la tua feature
3. Commit le tue modifiche
4. Push al branch
5. Apri una Pull Request

## 📞 Supporto

Per supporto o domande, contatta [<EMAIL>]
