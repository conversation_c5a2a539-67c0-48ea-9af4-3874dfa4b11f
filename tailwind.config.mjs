/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
      },
      colors: {
        primary: {
          DEFAULT: "#1b365d",
          light: "#2f5d91",
        },
        secondary: {
          DEFAULT: "#f26722",
          light: "#f79056",
        },
        background: "#f9fafc",
        surface: "#ffffff",
        text: {
          primary: "#1f2937",
          secondary: "#6b7280",
          onPrimary: "#ffffff",
        },
        success: "#22c55e",
        error: "#ef4444",
        warning: "#facc15",
      },
    },
  },
  plugins: [],
};
