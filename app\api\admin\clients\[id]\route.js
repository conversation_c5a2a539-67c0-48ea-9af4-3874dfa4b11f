import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";

// GET - Ottieni un singolo cliente
export async function GET(request, { params }) {
  try {
    const session = await getServerSession();

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const { id } = params;

    const client = await prisma.client.findUnique({
      where: { id },
      include: {
        tickets: {
          orderBy: { createdAt: "desc" },
          take: 10,
        },
        _count: {
          select: { tickets: true }
        }
      },
    });

    if (!client) {
      return NextResponse.json({ error: "Cliente non trovato" }, { status: 404 });
    }

    return NextResponse.json(client);
  } catch (error) {
    console.error("Errore nel caricamento del cliente:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}

// PUT - Aggiorna un cliente
export async function PUT(request, { params }) {
  try {
    const session = await getServerSession();

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const { id } = params;
    const data = await request.json();

    // Validazione campi obbligatori
    if (!data.name) {
      return NextResponse.json(
        { error: "Il nome del cliente è obbligatorio" },
        { status: 400 }
      );
    }

    const client = await prisma.client.update({
      where: { id },
      data: {
        name: data.name,
        email: data.email || null,
        phone: data.phone || null,
        address: data.address || null,
        city: data.city || null,
        postalCode: data.postalCode || null,
        company: data.company || null,
        notes: data.notes || null,
        isActive: data.isActive !== undefined ? data.isActive : true,
      },
    });

    return NextResponse.json(client);
  } catch (error) {
    if (error.code === "P2025") {
      return NextResponse.json({ error: "Cliente non trovato" }, { status: 404 });
    }
    console.error("Errore nell'aggiornamento del cliente:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}

// DELETE - Elimina un cliente
export async function DELETE(request, { params }) {
  try {
    const session = await getServerSession();

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const { id } = params;

    // Verifica se il cliente ha ticket associati
    const ticketsCount = await prisma.ticket.count({
      where: { clientId: id },
    });

    if (ticketsCount > 0) {
      return NextResponse.json(
        { 
          error: "Impossibile eliminare il cliente. Ha dei ticket associati.",
          ticketsCount 
        },
        { status: 400 }
      );
    }

    await prisma.client.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Cliente eliminato con successo" });
  } catch (error) {
    if (error.code === "P2025") {
      return NextResponse.json({ error: "Cliente non trovato" }, { status: 404 });
    }
    console.error("Errore nell'eliminazione del cliente:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
} 