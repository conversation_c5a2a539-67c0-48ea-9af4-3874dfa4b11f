import { withAuth } from "next-auth/middleware";
import { authOptions } from "@/lib/auth";

export default withAuth(
  function middleware(req) {
    // Middleware logic here if needed
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Permetti l'accesso alle route pubbliche
        if (pathname.startsWith("/auth/") || pathname === "/") {
          return true;
        }

        // Richiedi autenticazione per le route della dashboard
        if (pathname.startsWith("/dashboard/")) {
          if (!token) return false;

          // Controlla i permessi per le route admin
          if (pathname.startsWith("/dashboard/admin/")) {
            return token.role === "ADMIN";
          }

          // Controlla i permessi per le route employee
          if (pathname.startsWith("/dashboard/employee/")) {
            return token.role === "EMPLOYEE";
          }

          return true;
        }

        // Richiedi autenticazione per le API (escludi NextAuth)
        if (pathname.startsWith("/api/") && !pathname.startsWith("/api/auth/")) {
          return !!token;
        }

        return true;
      },
    },
    ...authOptions,
  }
);

export const config = {
  matcher: ["/dashboard/:path*", "/api/:path*"],
};