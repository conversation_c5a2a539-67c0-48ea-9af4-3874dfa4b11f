"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import DashboardLayout from "@/app/components/DashboardLayout";
import {
  Users,
  Ticket,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  UserCheck,
} from "lucide-react";

export default function AdminDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState({
    totalClients: 0,
    totalEmployees: 0,
    totalTickets: 0,
    openTickets: 0,
    completedTickets: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
  });
  const [recentTickets, setRecentTickets] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, ticketsRes] = await Promise.all([
        fetch("/api/admin/stats"),
        fetch("/api/admin/recent-tickets"),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      }

      if (ticketsRes.ok) {
        const ticketsData = await ticketsRes.json();
        setRecentTickets(ticketsData);
      }
    } catch (error) {
      console.error("Errore nel caricamento dei dati:", error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      title: "Clienti Totali",
      value: stats.totalClients,
      icon: Users,
      color: "bg-blue-500",
      change: "+12%",
    },
    {
      title: "Dipendenti Attivi",
      value: stats.totalEmployees,
      icon: UserCheck,
      color: "bg-green-500",
      change: "+2%",
    },
    {
      title: "Ticket Aperti",
      value: stats.openTickets,
      icon: Clock,
      color: "bg-yellow-500",
      change: "-5%",
    },
    {
      title: "Ticket Completati",
      value: stats.completedTickets,
      icon: CheckCircle,
      color: "bg-green-600",
      change: "+18%",
    },
    {
      title: "Fatturato Totale",
      value: `€${stats.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: "bg-primary",
      change: "+25%",
    },
    {
      title: "Fatturato Mensile",
      value: `€${stats.monthlyRevenue.toLocaleString()}`,
      icon: TrendingUp,
      color: "bg-secondary",
      change: "+15%",
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case "OPEN":
        return "bg-yellow-100 text-yellow-800";
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800";
      case "COMPLETED":
        return "bg-green-100 text-green-800";
      case "CLOSED":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "LOW":
        return "bg-gray-100 text-gray-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "HIGH":
        return "bg-orange-100 text-orange-800";
      case "URGENT":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary to-primary-light rounded-xl p-6 text-text-onPrimary">
          <h1 className="text-2xl font-bold mb-2">
            Benvenuto, {session?.user?.firstName}!
          </h1>
          <p className="text-primary-light/80">
            Ecco una panoramica delle attività della tua azienda
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {statCards.map((stat, index) => (
            <div key={index} className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold text-text-primary mt-1">{stat.value}</p>
                  <p className="text-success text-sm mt-1">{stat.change} vs mese scorso</p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Tickets */}
        <div className="bg-surface rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-text-primary">Ticket Recenti</h2>
              <a
                href="/dashboard/admin/tickets"
                className="text-primary hover:text-primary-light text-sm font-medium"
              >
                Vedi tutti
              </a>
            </div>
          </div>
          <div className="p-6">
            {recentTickets.length === 0 ? (
              <div className="text-center py-8">
                <Ticket className="h-12 w-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">Nessun ticket recente</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentTickets.map((ticket) => (
                  <div
                    key={ticket.id}
                    className="flex items-center justify-between p-4 bg-background rounded-lg"
                  >
                    <div className="flex-1">
                      <h3 className="font-medium text-text-primary">{ticket.title}</h3>
                      <p className="text-sm text-text-secondary mt-1">
                        Cliente: {ticket.client?.name}
                      </p>
                      <p className="text-sm text-text-secondary">
                        Assegnato a: {ticket.assignedTo?.firstName} {ticket.assignedTo?.lastName}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                        {ticket.priority}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                        {ticket.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <a
            href="/dashboard/admin/clients/new"
            className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center gap-4">
              <div className="bg-primary/10 p-3 rounded-lg group-hover:bg-primary/20 transition-colors">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-text-primary">Nuovo Cliente</h3>
                <p className="text-sm text-text-secondary">Aggiungi un nuovo cliente</p>
              </div>
            </div>
          </a>

          <a
            href="/dashboard/admin/employees/new"
            className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center gap-4">
              <div className="bg-secondary/10 p-3 rounded-lg group-hover:bg-secondary/20 transition-colors">
                <UserCheck className="h-6 w-6 text-secondary" />
              </div>
              <div>
                <h3 className="font-semibold text-text-primary">Nuovo Dipendente</h3>
                <p className="text-sm text-text-secondary">Aggiungi un nuovo dipendente</p>
              </div>
            </div>
          </a>

          <a
            href="/dashboard/admin/tickets/new"
            className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
          >
            <div className="flex items-center gap-4">
              <div className="bg-success/10 p-3 rounded-lg group-hover:bg-success/20 transition-colors">
                <Ticket className="h-6 w-6 text-success" />
              </div>
              <div>
                <h3 className="font-semibold text-text-primary">Nuovo Ticket</h3>
                <p className="text-sm text-text-secondary">Crea un nuovo ticket</p>
              </div>
            </div>
          </a>
        </div>
      </div>
    </DashboardLayout>
  );
} 