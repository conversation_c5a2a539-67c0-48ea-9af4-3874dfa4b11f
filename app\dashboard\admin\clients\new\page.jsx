"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/app/components/DashboardLayout";
import Link from "next/link";
import { ArrowLeft, Save, User, Mail, Phone, MapPin, Building, FileText } from "lucide-react";

export default function NewClientPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    postalCode: "",
    company: "",
    notes: "",
    isActive: true,
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/admin/clients", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/dashboard/admin/clients");
      } else {
        const error = await response.json();
        alert(error.error || "Errore nella creazione del cliente");
      }
    } catch (error) {
      console.error("Errore:", error);
      alert("Errore nella creazione del cliente");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link
            href="/dashboard/admin/clients"
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-text-primary">Nuovo Cliente</h1>
            <p className="text-text-secondary">Aggiungi un nuovo cliente al sistema</p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informazioni Principali */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <User className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">
                    Informazioni Principali
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Nome Cliente *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Nome del cliente"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Azienda
                    </label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="text"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="Nome dell'azienda"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Contatti */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <Mail className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Contatti</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Email
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Telefono
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="+39 ************"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Indirizzo */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <MapPin className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Indirizzo</h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Indirizzo
                    </label>
                    <input
                      type="text"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Via Roma 123"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Città
                      </label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="input"
                        placeholder="Milano"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        CAP
                      </label>
                      <input
                        type="text"
                        name="postalCode"
                        value={formData.postalCode}
                        onChange={handleInputChange}
                        className="input"
                        placeholder="20100"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Note */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Note</h2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Note aggiuntive
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={4}
                    className="input resize-none"
                    placeholder="Aggiungi note o informazioni aggiuntive sul cliente..."
                  />
                </div>
              </div>

              {/* Status */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-text-primary mb-4">Status</h2>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 text-sm text-text-primary">
                    Cliente attivo
                  </label>
                </div>
              </div>

              {/* Actions */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-text-onPrimary border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save size={16} />
                    )}
                    {loading ? "Salvataggio..." : "Salva Cliente"}
                  </button>

                  <Link
                    href="/dashboard/admin/clients"
                    className="w-full btn-secondary bg-gray-500 hover:bg-gray-600 text-white flex items-center justify-center gap-2"
                  >
                    Annulla
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
} 