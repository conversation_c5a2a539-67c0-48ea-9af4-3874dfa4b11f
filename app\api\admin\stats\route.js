import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession();

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    // Calcola le statistiche
    const [
      totalClients,
      totalEmployees,
      totalTickets,
      openTickets,
      completedTickets,
      revenueData,
    ] = await Promise.all([
      prisma.client.count({ where: { isActive: true } }),
      prisma.user.count({ where: { role: "EMPLOYEE", isActive: true } }),
      prisma.ticket.count(),
      prisma.ticket.count({ where: { status: "OPEN" } }),
      prisma.ticket.count({ where: { status: "COMPLETED" } }),
      prisma.ticket.aggregate({
        _sum: { totalCost: true },
        where: { status: "COMPLETED", totalCost: { not: null } },
      }),
    ]);

    // Calcola il fatturato mensile (ultimo mese)
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const monthlyRevenueData = await prisma.ticket.aggregate({
      _sum: { totalCost: true },
      where: {
        status: "COMPLETED",
        totalCost: { not: null },
        completedAt: { gte: lastMonth },
      },
    });

    const stats = {
      totalClients,
      totalEmployees,
      totalTickets,
      openTickets,
      completedTickets,
      totalRevenue: revenueData._sum.totalCost || 0,
      monthlyRevenue: monthlyRevenueData._sum.totalCost || 0,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Errore nel caricamento delle statistiche:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
} 