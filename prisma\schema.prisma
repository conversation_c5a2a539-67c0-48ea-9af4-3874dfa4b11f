// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  EMPLOYEE
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  CLOSED
  CANCELLED
}

enum TicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Models
model User {
  id          String    @id @default(cuid())
  name        String?   // Required by NextAuth
  username    String    @unique
  email       String?   @unique
  emailVerified DateTime?
  image       String?   // Required by NextAuth
  password    String
  firstName   String
  lastName    String
  role        UserRole  @default(EMPLOYEE)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  assignedTickets Ticket[] @relation("AssignedEmployee")
  createdTickets  Ticket[] @relation("CreatedByAdmin")
  createdClients  Client[] @relation("CreatedByAdmin")
  accounts        Account[]
  sessions        Session[]

  @@map("users")
}

model Client {
  id          String   @id @default(cuid())
  name        String
  email       String?
  phone       String?
  address     String?
  city        String?
  postalCode  String?
  company     String?
  notes       String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  createdBy   User     @relation("CreatedByAdmin", fields: [createdById], references: [id])
  createdById String
  tickets     Ticket[]

  @@map("clients")
}

model Ticket {
  id          String         @id @default(cuid())
  title       String
  description String
  status      TicketStatus   @default(OPEN)
  priority    TicketPriority @default(MEDIUM)
  estimatedHours Float?
  actualHours    Float?
  hourlyRate     Float?
  totalCost      Float?
  dueDate     DateTime?
  completedAt DateTime?
  notes       String?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  client        Client  @relation(fields: [clientId], references: [id])
  clientId      String
  assignedTo    User?   @relation("AssignedEmployee", fields: [assignedToId], references: [id])
  assignedToId  String?
  createdBy     User    @relation("CreatedByAdmin", fields: [createdById], references: [id])
  createdById   String

  @@map("tickets")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}
