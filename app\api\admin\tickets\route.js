import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET - Lista tutti i ticket
export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const tickets = await prisma.ticket.findMany({
      orderBy: { createdAt: "desc" },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        },
        createdBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    return NextResponse.json(tickets);
  } catch (error) {
    console.error("Errore nel caricamento dei ticket:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}

// POST - Crea un nuovo ticket
export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const data = await request.json();

    // Validazione campi obbligatori
    if (!data.title || !data.clientId) {
      return NextResponse.json(
        { error: "Titolo e cliente sono obbligatori" },
        { status: 400 }
      );
    }

    // Verifica che il cliente esista
    const client = await prisma.client.findUnique({
      where: { id: data.clientId }
    });

    if (!client) {
      return NextResponse.json(
        { error: "Cliente non trovato" },
        { status: 400 }
      );
    }

    // Se assegnato, verifica che il dipendente esista
    if (data.assignedToId) {
      const employee = await prisma.user.findFirst({
        where: {
          id: data.assignedToId,
          role: "EMPLOYEE",
          isActive: true
        }
      });

      if (!employee) {
        return NextResponse.json(
          { error: "Dipendente non trovato o non attivo" },
          { status: 400 }
        );
      }
    }

    const ticket = await prisma.ticket.create({
      data: {
        title: data.title,
        description: data.description || null,
        priority: data.priority || "MEDIUM",
        status: data.status || "OPEN",
        estimatedHours: data.estimatedHours || null,
        hourlyRate: data.hourlyRate || null,
        clientId: data.clientId,
        assignedToId: data.assignedToId || null,
        createdById: session.user.id,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            company: true
          }
        },
        assignedTo: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(ticket, { status: 201 });
  } catch (error) {
    console.error("Errore nella creazione del ticket:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}