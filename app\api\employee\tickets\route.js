import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession();

    if (!session || session.user.role !== "EMPLOYEE") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const userId = session.user.id;

    const tickets = await prisma.ticket.findMany({
      where: { assignedToId: userId },
      include: {
        client: { select: { name: true } },
      },
      orderBy: [
        { status: "asc" }, // Prima i ticket aperti
        { priority: "desc" }, // Poi per priorità
        { createdAt: "desc" }, // Infine per data
      ],
    });

    return NextResponse.json(tickets);
  } catch (error) {
    console.error("Errore nel caricamento dei ticket:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
} 