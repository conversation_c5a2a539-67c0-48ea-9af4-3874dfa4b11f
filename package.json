{"name": "gestionale", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:init": "node scripts/init-db.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.1.8", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "prisma": "^6.8.2", "tailwindcss": "^3.4.1"}}