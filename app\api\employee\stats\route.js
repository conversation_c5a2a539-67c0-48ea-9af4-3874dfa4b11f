import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "EMPLOYEE") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const userId = session.user.id;

    // Calcola le statistiche del dipendente
    const [
      assignedTickets,
      inProgressTickets,
      completedTickets,
      earningsData,
    ] = await Promise.all([
      prisma.ticket.count({
        where: { assignedToId: userId, status: { in: ["OPEN", "IN_PROGRESS"] } },
      }),
      prisma.ticket.count({
        where: { assignedToId: userId, status: "IN_PROGRESS" },
      }),
      prisma.ticket.count({
        where: { assignedToId: userId, status: "COMPLETED" },
      }),
      prisma.ticket.aggregate({
        _sum: { totalCost: true },
        where: {
          assignedToId: userId,
          status: "COMPLETED",
          totalCost: { not: null },
        },
      }),
    ]);

    const stats = {
      assignedTickets,
      inProgressTickets,
      completedTickets,
      totalEarnings: earningsData._sum.totalCost || 0,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Errore nel caricamento delle statistiche:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}