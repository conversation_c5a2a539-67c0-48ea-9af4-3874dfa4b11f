"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/app/components/DashboardLayout";
import Link from "next/link";
import { ArrowLeft, Save, User, Mail, Phone, Shield, Lock, Eye, EyeOff } from "lucide-react";

export default function NewEmployeePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    password: "",
    role: "EMPLOYEE",
    isActive: true,
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  const generatePassword = () => {
    const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setFormData(prev => ({ ...prev, password }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/admin/employees", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/dashboard/admin/employees");
      } else {
        const error = await response.json();
        alert(error.error || "Errore nella creazione del dipendente");
      }
    } catch (error) {
      console.error("Errore:", error);
      alert("Errore nella creazione del dipendente");
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link
            href="/dashboard/admin/employees"
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-text-primary">Nuovo Dipendente</h1>
            <p className="text-text-secondary">Aggiungi un nuovo dipendente al sistema</p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Informazioni Principali */}
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <User className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">
                    Informazioni Personali
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Nome *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Nome del dipendente"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Cognome *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Cognome del dipendente"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Contatti */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <Mail className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Contatti</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Email *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Telefono
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="+39 ************"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Credenziali */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <Lock className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Credenziali di Accesso</h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Password *
                    </label>
                    <div className="flex gap-2">
                      <div className="relative flex-1">
                        <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                        <input
                          type={showPassword ? "text" : "password"}
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          className="input pl-10 pr-10"
                          placeholder="Password sicura"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-secondary hover:text-text-primary"
                        >
                          {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                      <button
                        type="button"
                        onClick={generatePassword}
                        className="btn-secondary whitespace-nowrap"
                      >
                        Genera Password
                      </button>
                    </div>
                    <p className="text-xs text-text-secondary mt-1">
                      La password deve essere di almeno 8 caratteri
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Ruolo */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-4">
                  <Shield className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Ruolo e Permessi</h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Ruolo
                    </label>
                    <select
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      className="input"
                    >
                      <option value="EMPLOYEE">Dipendente</option>
                      <option value="ADMIN">Amministratore</option>
                    </select>
                  </div>

                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      {formData.role === "ADMIN" ? (
                        <>
                          <strong>Amministratore:</strong> Accesso completo a tutte le funzioni del sistema, 
                          gestione clienti, dipendenti e ticket.
                        </>
                      ) : (
                        <>
                          <strong>Dipendente:</strong> Può visualizzare e gestire solo i ticket assegnati.
                        </>
                      )}
                    </p>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-text-primary mb-4">Status</h2>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 text-sm text-text-primary">
                    Dipendente attivo
                  </label>
                </div>
                <p className="text-xs text-text-secondary mt-2">
                  I dipendenti inattivi non possono accedere al sistema
                </p>
              </div>

              {/* Actions */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50"
                  >
                    {loading ? (
                      <div className="w-4 h-4 border-2 border-text-onPrimary border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Save size={16} />
                    )}
                    {loading ? "Creazione..." : "Crea Dipendente"}
                  </button>

                  <Link
                    href="/dashboard/admin/employees"
                    className="w-full btn-secondary bg-gray-500 hover:bg-gray-600 text-white flex items-center justify-center gap-2"
                  >
                    Annulla
                  </Link>
                </div>

                {formData.password && (
                  <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      <strong>Nota:</strong> Comunica le credenziali al dipendente in modo sicuro.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
} 