const { PrismaClient } = require('../app/generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('Inizializzazione del database...');

  // Crea l'utente admin
  const hashedPassword = await bcrypt.hash('pass1234', 12);
  
  const admin = await prisma.user.upsert({
    where: { username: 'jojodev' },
    update: {},
    create: {
      name: '<PERSON><PERSON>',
      username: 'jojodev',
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'Principale',
      role: 'ADMIN',
      isActive: true,
    },
  });

  console.log('Utente admin creato:', admin);

  // Crea alcuni clienti di esempio
  const client1 = await prisma.client.upsert({
    where: { id: 'demo-client-1' },
    update: {},
    create: {
      id: 'demo-client-1',
      name: 'Studio Commercialista Rossi',
      email: '<EMAIL>',
      phone: '+39 02 1234567',
      address: 'Via Roma 123',
      city: 'Milano',
      postalCode: '20100',
      company: 'Studio Commercialista Rossi SRL',
      notes: 'Cliente storico, necessita supporto per sistemi contabili',
      createdById: admin.id,
    },
  });

  const client2 = await prisma.client.upsert({
    where: { id: 'demo-client-2' },
    update: {},
    create: {
      id: 'demo-client-2',
      name: 'Officina Meccanica Bianchi',
      email: '<EMAIL>',
      phone: '+39 06 9876543',
      address: 'Via Nazionale 456',
      city: 'Roma',
      postalCode: '00100',
      company: 'Bianchi Auto SNC',
      notes: 'Gestione rete e sistemi di fatturazione',
      createdById: admin.id,
    },
  });

  console.log('Clienti di esempio creati');

  // Crea un dipendente di esempio
  const employeePassword = await bcrypt.hash('employee123', 12);
  const employee = await prisma.user.upsert({
    where: { username: 'mario.rossi' },
    update: {},
    create: {
      name: 'Mario Rossi',
      username: 'mario.rossi',
      email: '<EMAIL>',
      password: employeePassword,
      firstName: 'Mario',
      lastName: 'Rossi',
      role: 'EMPLOYEE',
      isActive: true,
    },
  });

  console.log('Dipendente di esempio creato:', employee);

  // Crea alcuni ticket di esempio
  const ticket1 = await prisma.ticket.create({
    data: {
      title: 'Installazione antivirus aziendale',
      description: 'Installare e configurare antivirus su tutti i computer dell\'ufficio (15 postazioni)',
      status: 'OPEN',
      priority: 'HIGH',
      estimatedHours: 8,
      hourlyRate: 50,
      clientId: client1.id,
      createdById: admin.id,
      assignedToId: employee.id,
    },
  });

  const ticket2 = await prisma.ticket.create({
    data: {
      title: 'Configurazione backup automatico',
      description: 'Implementare sistema di backup automatico per i dati contabili',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      estimatedHours: 4,
      hourlyRate: 50,
      clientId: client1.id,
      createdById: admin.id,
      assignedToId: employee.id,
    },
  });

  const ticket3 = await prisma.ticket.create({
    data: {
      title: 'Creazione rete aziendale',
      description: 'Progettazione e implementazione della rete locale per la nuova sede',
      status: 'COMPLETED',
      priority: 'HIGH',
      estimatedHours: 16,
      actualHours: 18,
      hourlyRate: 60,
      totalCost: 1080,
      clientId: client2.id,
      createdById: admin.id,
      assignedToId: employee.id,
      completedAt: new Date('2024-01-15T10:00:00.000Z'),
    },
  });

  console.log('Ticket di esempio creati');
  console.log('Inizializzazione completata!');
  console.log('\nCredenziali di accesso:');
  console.log('Admin - Username: jojodev, Password: pass1234');
  console.log('Dipendente - Username: mario.rossi, Password: employee123');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 