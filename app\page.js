import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";

export default async function Home() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/auth/signin");
  }

  if (session.user.role === "ADMIN") {
    redirect("/dashboard/admin");
  } else {
    redirect("/dashboard/employee");
  }
}
