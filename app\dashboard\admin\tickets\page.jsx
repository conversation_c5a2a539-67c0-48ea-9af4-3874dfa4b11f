"use client";

import { useEffect, useState } from "react";
import DashboardLayout from "@/app/components/DashboardLayout";
import Link from "next/link";
import {
  Ticket,
  Plus,
  Search,
  Edit,
  Trash2,
  User,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Filter,
  Users,
  UserCheck,
} from "lucide-react";

const statusConfig = {
  OPEN: { 
    label: "Aperto", 
    icon: AlertCircle, 
    bgColor: "bg-blue-100", 
    textColor: "text-blue-800",
    iconColor: "text-blue-600"
  },
  IN_PROGRESS: { 
    label: "In Lavorazione", 
    icon: Clock, 
    bgColor: "bg-yellow-100", 
    textColor: "text-yellow-800",
    iconColor: "text-yellow-600"
  },
  COMPLETED: { 
    label: "Completato", 
    icon: CheckCircle, 
    bgColor: "bg-green-100", 
    textColor: "text-green-800",
    iconColor: "text-green-600"
  },
  CLOSED: { 
    label: "Chiuso", 
    icon: XCircle, 
    bgColor: "bg-gray-100", 
    textColor: "text-gray-800",
    iconColor: "text-gray-600"
  },
  CANCELLED: { 
    label: "Annullato", 
    icon: XCircle, 
    bgColor: "bg-red-100", 
    textColor: "text-red-800",
    iconColor: "text-red-600"
  }
};

const priorityConfig = {
  LOW: { label: "Bassa", bgColor: "bg-green-100", textColor: "text-green-800" },
  MEDIUM: { label: "Media", bgColor: "bg-yellow-100", textColor: "text-yellow-800" },
  HIGH: { label: "Alta", bgColor: "bg-orange-100", textColor: "text-orange-800" },
  URGENT: { label: "Urgente", bgColor: "bg-red-100", textColor: "text-red-800" }
};

export default function AdminTicketsPage() {
  const [tickets, setTickets] = useState([]);
  const [clients, setClients] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("");
  const [assigneeFilter, setAssigneeFilter] = useState("");
  const [filteredTickets, setFilteredTickets] = useState([]);

  useEffect(() => {
    fetchTickets();
    fetchClients();
    fetchEmployees();
  }, []);

  useEffect(() => {
    let filtered = tickets.filter((ticket) => {
      const matchesSearch = 
        ticket.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.assignedTo?.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.assignedTo?.lastName?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = !statusFilter || ticket.status === statusFilter;
      const matchesPriority = !priorityFilter || ticket.priority === priorityFilter;
      const matchesAssignee = !assigneeFilter || ticket.assignedToId === assigneeFilter;
      
      return matchesSearch && matchesStatus && matchesPriority && matchesAssignee;
    });
    
    setFilteredTickets(filtered);
  }, [tickets, searchTerm, statusFilter, priorityFilter, assigneeFilter]);

  const fetchTickets = async () => {
    try {
      const response = await fetch("/api/admin/tickets");
      if (response.ok) {
        const data = await response.json();
        setTickets(data);
      }
    } catch (error) {
      console.error("Errore nel caricamento dei ticket:", error);
    }
  };

  const fetchClients = async () => {
    try {
      const response = await fetch("/api/admin/clients");
      if (response.ok) {
        const data = await response.json();
        setClients(data);
      }
    } catch (error) {
      console.error("Errore nel caricamento dei clienti:", error);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch("/api/admin/employees");
      if (response.ok) {
        const data = await response.json();
        setEmployees(data.filter(emp => emp.role === "EMPLOYEE"));
      }
    } catch (error) {
      console.error("Errore nel caricamento dei dipendenti:", error);
    } finally {
      setLoading(false);
    }
  };

  const deleteTicket = async (ticketId) => {
    if (!confirm("Sei sicuro di voler eliminare questo ticket?")) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/tickets/${ticketId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchTickets();
      } else {
        const error = await response.json();
        alert(error.error || "Errore nell'eliminazione del ticket");
      }
    } catch (error) {
      console.error("Errore nell'eliminazione:", error);
      alert("Errore nell'eliminazione del ticket");
    }
  };

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setPriorityFilter("");
    setAssigneeFilter("");
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-text-primary">Gestione Ticket</h1>
            <p className="text-text-secondary">
              Gestisci tutti i ticket di assistenza
            </p>
          </div>
          <Link
            href="/dashboard/admin/tickets/new"
            className="btn-primary flex items-center gap-2"
          >
            <Plus size={20} />
            Nuovo Ticket
          </Link>
        </div>

        {/* Filters */}
        <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-primary" />
              <h3 className="font-semibold">Filtri</h3>
            </div>
            <button
              onClick={resetFilters}
              className="text-sm text-primary hover:underline"
            >
              Pulisci Filtri
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative lg:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
              <input
                type="text"
                placeholder="Cerca per titolo, cliente o assegnatario..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
              />
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input text-sm"
            >
              <option value="">Tutti gli stati</option>
              {Object.entries(statusConfig).map(([status, config]) => (
                <option key={status} value={status}>{config.label}</option>
              ))}
            </select>

            {/* Priority Filter */}
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="input text-sm"
            >
              <option value="">Tutte le priorità</option>
              {Object.entries(priorityConfig).map(([priority, config]) => (
                <option key={priority} value={priority}>{config.label}</option>
              ))}
            </select>

            {/* Assignee Filter */}
            <select
              value={assigneeFilter}
              onChange={(e) => setAssigneeFilter(e.target.value)}
              className="input text-sm"
            >
              <option value="">Tutti i dipendenti</option>
              <option value="null">Non assegnati</option>
              {employees.map((employee) => (
                <option key={employee.id} value={employee.id}>
                  {employee.firstName} {employee.lastName}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Ticket Totali</p>
                <p className="text-2xl font-bold text-text-primary">{tickets.length}</p>
              </div>
              <div className="bg-blue-500 p-3 rounded-lg">
                <Ticket className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
          
          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Aperti</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => t.status === "OPEN").length}
                </p>
              </div>
              <div className="bg-orange-500 p-3 rounded-lg">
                <AlertCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">In Lavorazione</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => t.status === "IN_PROGRESS").length}
                </p>
              </div>
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Non Assegnati</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => !t.assignedToId).length}
                </p>
              </div>
              <div className="bg-red-500 p-3 rounded-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Tickets List */}
        <div className="bg-surface rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-text-primary">
              Lista Ticket ({filteredTickets.length})
            </h2>
          </div>
          
          {filteredTickets.length === 0 ? (
            <div className="text-center py-12">
              <Ticket className="h-12 w-12 text-text-secondary mx-auto mb-4" />
              <p className="text-text-secondary">
                {tickets.length === 0 ? "Nessun ticket presente" : "Nessun ticket trovato con i filtri attuali"}
              </p>
              {tickets.length === 0 && (
                <Link
                  href="/dashboard/admin/tickets/new"
                  className="btn-primary mt-4 inline-flex items-center gap-2"
                >
                  <Plus size={20} />
                  Crea il primo ticket
                </Link>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-background">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Ticket
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Cliente
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Assegnato a
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Priorità
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Data
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Azioni
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredTickets.map((ticket) => {
                    const StatusIcon = statusConfig[ticket.status]?.icon || Ticket;
                    return (
                      <tr key={ticket.id} className="hover:bg-background transition-colors">
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-text-primary">
                              {ticket.title}
                            </div>
                            <div className="text-sm text-text-secondary">
                              ID: {ticket.id.slice(-8)}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <User size={16} className="text-text-secondary" />
                            <span className="text-sm text-text-primary">
                              {ticket.client?.name || "N/A"}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          {ticket.assignedTo ? (
                            <div className="flex items-center gap-2">
                              <UserCheck size={16} className="text-green-600" />
                              <span className="text-sm text-text-primary">
                                {ticket.assignedTo.firstName} {ticket.assignedTo.lastName}
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-text-secondary italic">
                              Non assegnato
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            priorityConfig[ticket.priority]?.bgColor || "bg-gray-100"
                          } ${
                            priorityConfig[ticket.priority]?.textColor || "text-gray-800"
                          }`}>
                            {priorityConfig[ticket.priority]?.label || ticket.priority}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <StatusIcon 
                              size={16} 
                              className={statusConfig[ticket.status]?.iconColor || "text-gray-600"} 
                            />
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              statusConfig[ticket.status]?.bgColor || "bg-gray-100"
                            } ${
                              statusConfig[ticket.status]?.textColor || "text-gray-800"
                            }`}>
                              {statusConfig[ticket.status]?.label || ticket.status}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-1 text-sm text-text-secondary">
                            <Calendar size={14} />
                            {new Date(ticket.createdAt).toLocaleDateString("it-IT")}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <Link
                              href={`/dashboard/admin/tickets/${ticket.id}`}
                              className="p-2 text-primary hover:bg-primary/10 rounded-lg transition-colors"
                              title="Modifica ticket"
                            >
                              <Edit size={16} />
                            </Link>
                            <button
                              onClick={() => deleteTicket(ticket.id)}
                              className="p-2 text-error hover:bg-error/10 rounded-lg transition-colors"
                              title="Elimina ticket"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
} 