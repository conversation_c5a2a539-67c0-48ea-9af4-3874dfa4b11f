import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function PATCH(request, { params }) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "EMPLOYEE") {
      return NextResponse.json({ error: "Non autorizzato" }, { status: 401 });
    }

    const { id } = await params;
    const { status, actualHours } = await request.json();

    // Verifica che il ticket sia assegnato al dipendente
    const ticket = await prisma.ticket.findFirst({
      where: {
        id,
        assignedToId: session.user.id,
      },
    });

    if (!ticket) {
      return NextResponse.json({ error: "Ticket non trovato" }, { status: 404 });
    }

    // Prepara i dati per l'aggiornamento
    const updateData = { status };

    // Se il ticket viene completato, aggiungi la data di completamento e calcola il costo
    if (status === "COMPLETED") {
      updateData.completedAt = new Date();

      // Se sono state fornite le ore effettive, calcolale
      if (actualHours && ticket.hourlyRate) {
        updateData.actualHours = actualHours;
        updateData.totalCost = actualHours * ticket.hourlyRate;
      } else if (ticket.estimatedHours && ticket.hourlyRate) {
        // Usa le ore stimate se non sono state fornite quelle effettive
        updateData.actualHours = ticket.estimatedHours;
        updateData.totalCost = ticket.estimatedHours * ticket.hourlyRate;
      }
    }

    const updatedTicket = await prisma.ticket.update({
      where: { id },
      data: updateData,
      include: {
        client: { select: { name: true } },
      },
    });

    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error("Errore nell'aggiornamento del ticket:", error);
    return NextResponse.json(
      { error: "Errore interno del server" },
      { status: 500 }
    );
  }
}