// Script di test per verificare le API del gestionale
const { PrismaClient } = require('./app/generated/prisma');

const prisma = new PrismaClient();

async function testDatabase() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test connessione database
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Test conteggio utenti
    const userCount = await prisma.user.count();
    console.log(`📊 Users in database: ${userCount}`);
    
    // Test conteggio clienti
    const clientCount = await prisma.client.count();
    console.log(`📊 Clients in database: ${clientCount}`);
    
    // Test conteggio ticket
    const ticketCount = await prisma.ticket.count();
    console.log(`📊 Tickets in database: ${ticketCount}`);
    
    // Test recupero admin user
    const adminUser = await prisma.user.findUnique({
      where: { username: 'jojodev' }
    });
    
    if (adminUser) {
      console.log('✅ Admin user found:', {
        id: adminUser.id,
        username: adminUser.username,
        role: adminUser.role,
        isActive: adminUser.isActive
      });
    } else {
      console.log('❌ Admin user not found');
    }
    
    // Test recupero employee user
    const employeeUser = await prisma.user.findUnique({
      where: { username: 'mario.rossi' }
    });
    
    if (employeeUser) {
      console.log('✅ Employee user found:', {
        id: employeeUser.id,
        username: employeeUser.username,
        role: employeeUser.role,
        isActive: employeeUser.isActive
      });
    } else {
      console.log('❌ Employee user not found');
    }
    
    console.log('\n🎉 Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Esegui il test
testDatabase();
