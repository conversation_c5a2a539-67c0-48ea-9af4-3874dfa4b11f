"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import DashboardLayout from "@/app/components/DashboardLayout";
import {
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  DollarSign,
  Play,
  Check,
} from "lucide-react";

export default function EmployeeDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState({
    assignedTickets: 0,
    inProgressTickets: 0,
    completedTickets: 0,
    totalEarnings: 0,
  });
  const [myTickets, setMyTickets] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsRes, ticketsRes] = await Promise.all([
        fetch("/api/employee/stats"),
        fetch("/api/employee/tickets"),
      ]);

      if (statsRes.ok) {
        const statsData = await statsRes.json();
        setStats(statsData);
      }

      if (ticketsRes.ok) {
        const ticketsData = await ticketsRes.json();
        setMyTickets(ticketsData);
      }
    } catch (error) {
      console.error("Errore nel caricamento dei dati:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateTicketStatus = async (ticketId, newStatus) => {
    try {
      const response = await fetch(`/api/employee/tickets/${ticketId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchDashboardData(); // Ricarica i dati
      }
    } catch (error) {
      console.error("Errore nell'aggiornamento del ticket:", error);
    }
  };

  const statCards = [
    {
      title: "Ticket Assegnati",
      value: stats.assignedTickets,
      icon: Clock,
      color: "bg-blue-500",
    },
    {
      title: "In Lavorazione",
      value: stats.inProgressTickets,
      icon: AlertCircle,
      color: "bg-yellow-500",
    },
    {
      title: "Completati",
      value: stats.completedTickets,
      icon: CheckCircle,
      color: "bg-green-500",
    },
    {
      title: "Guadagni Totali",
      value: `€${stats.totalEarnings.toLocaleString()}`,
      icon: DollarSign,
      color: "bg-primary",
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case "OPEN":
        return "bg-yellow-100 text-yellow-800";
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800";
      case "COMPLETED":
        return "bg-green-100 text-green-800";
      case "CLOSED":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "LOW":
        return "bg-gray-100 text-gray-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "HIGH":
        return "bg-orange-100 text-orange-800";
      case "URGENT":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("it-IT");
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-primary to-primary-light rounded-xl p-6 text-text-onPrimary">
          <h1 className="text-2xl font-bold mb-2">
            Benvenuto, {session?.user?.firstName}!
          </h1>
          <p className="text-primary-light/80">
            Ecco i tuoi ticket assegnati e il tuo progresso
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => (
            <div key={index} className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold text-text-primary mt-1">{stat.value}</p>
                </div>
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* My Tickets */}
        <div className="bg-surface rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-text-primary">I Miei Ticket</h2>
          </div>
          <div className="p-6">
            {myTickets.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">Nessun ticket assegnato</p>
              </div>
            ) : (
              <div className="space-y-4">
                {myTickets.map((ticket) => (
                  <div
                    key={ticket.id}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-text-primary mb-1">{ticket.title}</h3>
                        <p className="text-sm text-text-secondary mb-2">{ticket.description}</p>
                        <div className="flex items-center gap-4 text-sm text-text-secondary">
                          <span>Cliente: {ticket.client?.name}</span>
                          <span className="flex items-center gap-1">
                            <Calendar size={14} />
                            Creato: {formatDate(ticket.createdAt)}
                          </span>
                          {ticket.dueDate && (
                            <span className="flex items-center gap-1">
                              <Clock size={14} />
                              Scadenza: {formatDate(ticket.dueDate)}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(ticket.priority)}`}>
                          {ticket.priority}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ticket.status)}`}>
                          {ticket.status}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-text-secondary">
                        {ticket.estimatedHours && (
                          <span>Ore stimate: {ticket.estimatedHours}h</span>
                        )}
                        {ticket.hourlyRate && (
                          <span>Tariffa: €{ticket.hourlyRate}/h</span>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        {ticket.status === "OPEN" && (
                          <button
                            onClick={() => updateTicketStatus(ticket.id, "IN_PROGRESS")}
                            className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
                          >
                            <Play size={14} />
                            Inizia
                          </button>
                        )}
                        {ticket.status === "IN_PROGRESS" && (
                          <button
                            onClick={() => updateTicketStatus(ticket.id, "COMPLETED")}
                            className="flex items-center gap-1 px-3 py-1 bg-green-500 text-white text-sm rounded-lg hover:bg-green-600 transition-colors"
                          >
                            <Check size={14} />
                            Completa
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 