import { Inter } from "next/font/google";
import "./globals.css";
import { getServerSession } from "next-auth";
import SessionProvider from "./components/SessionProvider";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "Gestionale IT Solutions",
  description: "Sistema di gestione per aziende IT - Clienti, Dipendenti e Ticket",
};

export default async function RootLayout({ children }) {
  const session = await getServerSession();

  return (
    <html lang="it" className={inter.className}>
      <body className="antialiased bg-background text-text-primary">
        <SessionProvider session={session}>
          {children}
        </SessionProvider>
      </body>
    </html>
  );
}
