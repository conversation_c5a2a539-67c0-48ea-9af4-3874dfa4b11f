// Script di test per verificare l'autenticazione
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('./app/generated/prisma');

const prisma = new PrismaClient();

async function testAuthentication() {
  console.log('🔐 Testing authentication...');
  
  try {
    // Test password admin
    const adminUser = await prisma.user.findUnique({
      where: { username: 'jojodev' }
    });
    
    if (adminUser) {
      const isAdminPasswordValid = await bcrypt.compare('pass1234', adminUser.password);
      console.log(`✅ Admin password test: ${isAdminPasswordValid ? 'PASSED' : 'FAILED'}`);
    }
    
    // Test password employee
    const employeeUser = await prisma.user.findUnique({
      where: { username: 'mario.rossi' }
    });
    
    if (employeeUser) {
      const isEmployeePasswordValid = await bcrypt.compare('employee123', employeeUser.password);
      console.log(`✅ Employee password test: ${isEmployeePasswordValid ? 'PASSED' : 'FAILED'}`);
    }
    
    // Test password errata
    const isWrongPasswordValid = await bcrypt.compare('wrongpassword', adminUser.password);
    console.log(`✅ Wrong password test: ${!isWrongPasswordValid ? 'PASSED' : 'FAILED'}`);
    
    console.log('\n🎉 Authentication test completed!');
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Esegui il test
testAuthentication();
