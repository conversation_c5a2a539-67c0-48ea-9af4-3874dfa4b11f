"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import DashboardLayout from "@/app/components/DashboardLayout";
import Link from "next/link";
import { ArrowLeft, Save, User, Mail, Phone, MapPin, Building, FileText, Ticket } from "lucide-react";

export default function EditClientPage() {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [client, setClient] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    postalCode: "",
    company: "",
    notes: "",
    isActive: true,
  });

  useEffect(() => {
    if (params.id) {
      fetchClient();
    }
  }, [params.id]);

  const fetchClient = async () => {
    try {
      const response = await fetch(`/api/admin/clients/${params.id}`);
      if (response.ok) {
        const clientData = await response.json();
        setClient(clientData);
        setFormData({
          name: clientData.name || "",
          email: clientData.email || "",
          phone: clientData.phone || "",
          address: clientData.address || "",
          city: clientData.city || "",
          postalCode: clientData.postalCode || "",
          company: clientData.company || "",
          notes: clientData.notes || "",
          isActive: clientData.isActive,
        });
      } else {
        alert("Cliente non trovato");
        router.push("/dashboard/admin/clients");
      }
    } catch (error) {
      console.error("Errore nel caricamento del cliente:", error);
      alert("Errore nel caricamento del cliente");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      const response = await fetch(`/api/admin/clients/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        router.push("/dashboard/admin/clients");
      } else {
        const error = await response.json();
        alert(error.error || "Errore nell'aggiornamento del cliente");
      }
    } catch (error) {
      console.error("Errore:", error);
      alert("Errore nell'aggiornamento del cliente");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!client) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <p className="text-text-secondary">Cliente non trovato</p>
          <Link href="/dashboard/admin/clients" className="btn-primary mt-4">
            Torna alla lista
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link
            href="/dashboard/admin/clients"
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-text-primary">
              Modifica Cliente: {client.name}
            </h1>
            <p className="text-text-secondary">
              Cliente dal {new Date(client.createdAt).toLocaleDateString("it-IT")}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Form Principale */}
          <div className="lg:col-span-3">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Informazioni Principali */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <User className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">
                    Informazioni Principali
                  </h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Nome Cliente *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Nome del cliente"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Azienda
                    </label>
                    <div className="relative">
                      <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="text"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="Nome dell'azienda"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Contatti */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <Mail className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Contatti</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Email
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Telefono
                    </label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="input pl-10"
                        placeholder="+39 ************"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Indirizzo */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-6">
                  <MapPin className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Indirizzo</h2>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Indirizzo
                    </label>
                    <input
                      type="text"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Via Roma 123"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        Città
                      </label>
                      <input
                        type="text"
                        name="city"
                        value={formData.city}
                        onChange={handleInputChange}
                        className="input"
                        placeholder="Milano"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">
                        CAP
                      </label>
                      <input
                        type="text"
                        name="postalCode"
                        value={formData.postalCode}
                        onChange={handleInputChange}
                        className="input"
                        placeholder="20100"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Note */}
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="text-primary" size={20} />
                  <h2 className="text-lg font-semibold text-text-primary">Note</h2>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Note aggiuntive
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={4}
                    className="input resize-none"
                    placeholder="Aggiungi note o informazioni aggiuntive sul cliente..."
                  />
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-4">
                <button
                  type="submit"
                  disabled={saving}
                  className="btn-primary flex items-center gap-2 disabled:opacity-50"
                >
                  {saving ? (
                    <div className="w-4 h-4 border-2 border-text-onPrimary border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save size={16} />
                  )}
                  {saving ? "Salvataggio..." : "Salva Modifiche"}
                </button>

                <Link
                  href="/dashboard/admin/clients"
                  className="btn-secondary bg-gray-500 hover:bg-gray-600 text-white flex items-center gap-2"
                >
                  Annulla
                </Link>
              </div>
            </form>
          </div>

          {/* Sidebar Info */}
          <div className="space-y-6">
            {/* Status */}
            <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
              <h2 className="text-lg font-semibold text-text-primary mb-4">Status</h2>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 text-sm text-text-primary">
                  Cliente attivo
                </label>
              </div>
            </div>

            {/* Statistiche */}
            <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center gap-2 mb-4">
                <Ticket className="text-primary" size={20} />
                <h2 className="text-lg font-semibold text-text-primary">Statistiche</h2>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-text-secondary">Ticket totali:</span>
                  <span className="font-medium">{client._count?.tickets || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Cliente dal:</span>
                  <span className="font-medium">
                    {new Date(client.createdAt).toLocaleDateString("it-IT")}
                  </span>
                </div>
              </div>
            </div>

            {/* Ticket Recenti */}
            {client.tickets && client.tickets.length > 0 && (
              <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
                <h2 className="text-lg font-semibold text-text-primary mb-4">
                  Ticket Recenti
                </h2>
                
                <div className="space-y-3">
                  {client.tickets.slice(0, 5).map((ticket) => (
                    <div key={ticket.id} className="border-l-2 border-primary pl-3">
                      <p className="text-sm font-medium text-text-primary">
                        {ticket.title}
                      </p>
                      <p className="text-xs text-text-secondary">
                        {new Date(ticket.createdAt).toLocaleDateString("it-IT")}
                      </p>
                    </div>
                  ))}
                </div>

                <Link
                  href={`/dashboard/admin/tickets?client=${client.id}`}
                  className="text-primary text-sm hover:underline mt-3 inline-block"
                >
                  Vedi tutti i ticket →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 