"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import DashboardLayout from "@/app/components/DashboardLayout";
import {
  Ticket,
  Search,
  User,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Filter,
  Timer,
  Euro,
  FileText,
  PlayCircle,
  PauseCircle,
  Save,
} from "lucide-react";

const statusConfig = {
  OPEN: { 
    label: "Aperto", 
    icon: AlertCircle, 
    bgColor: "bg-blue-100", 
    textColor: "text-blue-800",
    iconColor: "text-blue-600"
  },
  IN_PROGRESS: { 
    label: "In Lavorazione", 
    icon: Clock, 
    bgColor: "bg-yellow-100", 
    textColor: "text-yellow-800",
    iconColor: "text-yellow-600"
  },
  COMPLETED: { 
    label: "Completato", 
    icon: CheckCircle, 
    bgColor: "bg-green-100", 
    textColor: "text-green-800",
    iconColor: "text-green-600"
  },
  CLOSED: { 
    label: "Chiuso", 
    icon: XCircle, 
    bgColor: "bg-gray-100", 
    textColor: "text-gray-800",
    iconColor: "text-gray-600"
  },
  CANCELLED: { 
    label: "Annullato", 
    icon: XCircle, 
    bgColor: "bg-red-100", 
    textColor: "text-red-800",
    iconColor: "text-red-600"
  }
};

const priorityConfig = {
  LOW: { label: "Bassa", bgColor: "bg-green-100", textColor: "text-green-800" },
  MEDIUM: { label: "Media", bgColor: "bg-yellow-100", textColor: "text-yellow-800" },
  HIGH: { label: "Alta", bgColor: "bg-orange-100", textColor: "text-orange-800" },
  URGENT: { label: "Urgente", bgColor: "bg-red-100", textColor: "text-red-800" }
};

export default function EmployeeTicketsPage() {
  const { data: session } = useSession();
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [priorityFilter, setPriorityFilter] = useState("");
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    fetchTickets();
  }, []);

  useEffect(() => {
    let filtered = tickets.filter((ticket) => {
      const matchesSearch = 
        ticket.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        ticket.client?.name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = !statusFilter || ticket.status === statusFilter;
      const matchesPriority = !priorityFilter || ticket.priority === priorityFilter;
      
      return matchesSearch && matchesStatus && matchesPriority;
    });
    
    setFilteredTickets(filtered);
  }, [tickets, searchTerm, statusFilter, priorityFilter]);

  const fetchTickets = async () => {
    try {
      const response = await fetch("/api/employee/tickets");
      if (response.ok) {
        const data = await response.json();
        setTickets(data);
      }
    } catch (error) {
      console.error("Errore nel caricamento dei ticket:", error);
    } finally {
      setLoading(false);
    }
  };

  const updateTicketStatus = async (ticketId, newStatus) => {
    setIsUpdating(true);
    try {
      const response = await fetch(`/api/employee/tickets/${ticketId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        fetchTickets();
        if (selectedTicket?.id === ticketId) {
          setSelectedTicket(prev => ({ ...prev, status: newStatus }));
        }
      } else {
        const error = await response.json();
        alert(error.error || "Errore nell'aggiornamento del ticket");
      }
    } catch (error) {
      console.error("Errore nell'aggiornamento:", error);
      alert("Errore nell'aggiornamento del ticket");
    } finally {
      setIsUpdating(false);
    }
  };

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("");
    setPriorityFilter("");
  };

  const getNextStatus = (currentStatus) => {
    switch (currentStatus) {
      case "OPEN":
        return "IN_PROGRESS";
      case "IN_PROGRESS":
        return "COMPLETED";
      default:
        return null;
    }
  };

  const getStatusButtonText = (status) => {
    switch (status) {
      case "OPEN":
        return "Inizia Lavorazione";
      case "IN_PROGRESS":
        return "Completa Ticket";
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-text-primary">I Miei Ticket</h1>
            <p className="text-text-secondary">
              Gestisci i ticket a te assegnati
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-primary" />
              <h3 className="font-semibold">Filtri</h3>
            </div>
            <button
              onClick={resetFilters}
              className="text-sm text-primary hover:underline"
            >
              Pulisci Filtri
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative lg:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" size={16} />
              <input
                type="text"
                placeholder="Cerca per titolo o cliente..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
              />
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input text-sm"
            >
              <option value="">Tutti gli stati</option>
              {Object.entries(statusConfig).map(([status, config]) => (
                <option key={status} value={status}>{config.label}</option>
              ))}
            </select>

            {/* Priority Filter */}
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="input text-sm"
            >
              <option value="">Tutte le priorità</option>
              {Object.entries(priorityConfig).map(([priority, config]) => (
                <option key={priority} value={priority}>{config.label}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Ticket Totali</p>
                <p className="text-2xl font-bold text-text-primary">{tickets.length}</p>
              </div>
              <div className="bg-blue-500 p-3 rounded-lg">
                <Ticket className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
          
          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Da Iniziare</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => t.status === "OPEN").length}
                </p>
              </div>
              <div className="bg-orange-500 p-3 rounded-lg">
                <AlertCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">In Lavorazione</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => t.status === "IN_PROGRESS").length}
                </p>
              </div>
              <div className="bg-yellow-500 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-text-secondary text-sm font-medium">Completati</p>
                <p className="text-2xl font-bold text-text-primary">
                  {tickets.filter(t => t.status === "COMPLETED").length}
                </p>
              </div>
              <div className="bg-green-500 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Tickets Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredTickets.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Ticket className="h-12 w-12 text-text-secondary mx-auto mb-4" />
              <p className="text-text-secondary">
                {tickets.length === 0 ? "Nessun ticket assegnato" : "Nessun ticket trovato con i filtri attuali"}
              </p>
            </div>
          ) : (
            filteredTickets.map((ticket) => {
              const StatusIcon = statusConfig[ticket.status]?.icon || Ticket;
              const nextStatus = getNextStatus(ticket.status);
              const buttonText = getStatusButtonText(ticket.status);
              
              return (
                <div key={ticket.id} className="bg-surface rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-text-primary mb-1">
                        {ticket.title}
                      </h3>
                      <p className="text-sm text-text-secondary">
                        ID: {ticket.id.slice(-8)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <StatusIcon 
                        size={16} 
                        className={statusConfig[ticket.status]?.iconColor || "text-gray-600"} 
                      />
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        statusConfig[ticket.status]?.bgColor || "bg-gray-100"
                      } ${
                        statusConfig[ticket.status]?.textColor || "text-gray-800"
                      }`}>
                        {statusConfig[ticket.status]?.label || ticket.status}
                      </span>
                    </div>
                  </div>

                  {/* Client and Priority */}
                  <div className="space-y-3 mb-4">
                    <div className="flex items-center gap-2">
                      <User size={16} className="text-text-secondary" />
                      <span className="text-sm text-text-primary">
                        {ticket.client?.name || "N/A"}
                      </span>
                      {ticket.client?.company && (
                        <span className="text-sm text-text-secondary">
                          • {ticket.client.company}
                        </span>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Calendar size={16} className="text-text-secondary" />
                        <span className="text-sm text-text-secondary">
                          {new Date(ticket.createdAt).toLocaleDateString("it-IT")}
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        priorityConfig[ticket.priority]?.bgColor || "bg-gray-100"
                      } ${
                        priorityConfig[ticket.priority]?.textColor || "text-gray-800"
                      }`}>
                        {priorityConfig[ticket.priority]?.label || ticket.priority}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  {ticket.description && (
                    <div className="mb-4">
                      <p className="text-sm text-text-secondary line-clamp-3">
                        {ticket.description}
                      </p>
                    </div>
                  )}

                  {/* Cost Info */}
                  {(ticket.estimatedHours || ticket.hourlyRate) && (
                    <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-background rounded-lg">
                      {ticket.estimatedHours && (
                        <div className="flex items-center gap-2">
                          <Timer size={16} className="text-text-secondary" />
                          <div>
                            <p className="text-xs text-text-secondary">Ore stimate</p>
                            <p className="text-sm font-medium">{ticket.estimatedHours}h</p>
                          </div>
                        </div>
                      )}
                      {ticket.hourlyRate && (
                        <div className="flex items-center gap-2">
                          <Euro size={16} className="text-text-secondary" />
                          <div>
                            <p className="text-xs text-text-secondary">Tariffa oraria</p>
                            <p className="text-sm font-medium">€{ticket.hourlyRate}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="space-y-2">
                    {buttonText && nextStatus && (
                      <button
                        onClick={() => updateTicketStatus(ticket.id, nextStatus)}
                        disabled={isUpdating}
                        className="w-full btn-primary text-sm disabled:opacity-50 flex items-center justify-center gap-2"
                      >
                        {isUpdating ? (
                          <div className="w-4 h-4 border-2 border-text-onPrimary border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            {nextStatus === "IN_PROGRESS" ? (
                              <PlayCircle size={16} />
                            ) : (
                              <CheckCircle size={16} />
                            )}
                            {buttonText}
                          </>
                        )}
                      </button>
                    )}

                    <button
                      onClick={() => setSelectedTicket(ticket)}
                      className="w-full btn-secondary text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 flex items-center justify-center gap-2"
                    >
                      <FileText size={16} />
                      Vedi Dettagli
                    </button>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Ticket Detail Modal */}
        {selectedTicket && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-surface rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-text-primary">
                    Dettagli Ticket
                  </h2>
                  <button
                    onClick={() => setSelectedTicket(null)}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <XCircle size={20} />
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{selectedTicket.title}</h3>
                    <p className="text-sm text-text-secondary">ID: {selectedTicket.id}</p>
                  </div>

                  {selectedTicket.description && (
                    <div>
                      <h4 className="font-medium mb-2">Descrizione</h4>
                      <p className="text-text-secondary whitespace-pre-wrap">
                        {selectedTicket.description}
                      </p>
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Cliente</h4>
                      <p>{selectedTicket.client?.name}</p>
                      {selectedTicket.client?.company && (
                        <p className="text-sm text-text-secondary">
                          {selectedTicket.client.company}
                        </p>
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Data Creazione</h4>
                      <p>{new Date(selectedTicket.createdAt).toLocaleDateString("it-IT")}</p>
                    </div>
                  </div>

                  {(selectedTicket.estimatedHours || selectedTicket.hourlyRate) && (
                    <div>
                      <h4 className="font-medium mb-2">Dettagli Costo</h4>
                      <div className="grid grid-cols-2 gap-4">
                        {selectedTicket.estimatedHours && (
                          <div>
                            <p className="text-sm text-text-secondary">Ore stimate</p>
                            <p className="font-medium">{selectedTicket.estimatedHours} ore</p>
                          </div>
                        )}
                        {selectedTicket.hourlyRate && (
                          <div>
                            <p className="text-sm text-text-secondary">Tariffa oraria</p>
                            <p className="font-medium">€{selectedTicket.hourlyRate}</p>
                          </div>
                        )}
                      </div>
                      {selectedTicket.estimatedHours && selectedTicket.hourlyRate && (
                        <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                          <p className="text-sm font-medium text-blue-800">
                            Costo stimato: €{(selectedTicket.estimatedHours * selectedTicket.hourlyRate).toFixed(2)}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
} 